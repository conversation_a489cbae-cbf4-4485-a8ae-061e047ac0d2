/**
 * Test script để kiểm tra fix lỗi downloadUrl
 */

import { GoogleAuth } from './src/auth/google-auth.js';
import { googleDriveAPI } from './src/api/google-drive-api.js';
import dotenv from 'dotenv';

dotenv.config();

async function testDownloadUrlFix() {
    try {
        console.log('🧪 Testing downloadUrl fix...');
        
        // Test với một user có files
        const testUserEmail = '<EMAIL>';
        
        console.log(`📋 Testing with user: ${testUserEmail}`);
        
        // Test getFile method với một file ID cụ thể
        // L<PERSON>y danh sách files trước
        console.log('📁 Getting file list...');
        const fileList = await googleDriveAPI.listFiles(testUserEmail, {
            pageSize: 5,
            fields: 'files(id, name, mimeType, size)'
        });
        
        if (fileList.files.length === 0) {
            console.log('❌ No files found for user');
            return;
        }
        
        const testFile = fileList.files[0];
        console.log(`📄 Testing with file: ${testFile.name} (${testFile.id})`);
        
        // Test getFile method - đây là nơi lỗi downloadUrl xảy ra
        console.log('🔍 Testing getFile method...');
        const fileDetails = await googleDriveAPI.getFile(testUserEmail, testFile.id);
        
        console.log('✅ getFile method successful!');
        console.log(`   - File name: ${fileDetails.name}`);
        console.log(`   - MIME type: ${fileDetails.mimeType}`);
        console.log(`   - Size: ${fileDetails.size || 'N/A'}`);
        console.log(`   - Is Google Doc: ${fileDetails.isGoogleDoc}`);
        
        // Test với Google Docs file nếu có
        const googleDocFile = fileList.files.find(file => 
            googleDriveAPI.isGoogleDocsFormat(file.mimeType)
        );
        
        if (googleDocFile) {
            console.log(`📄 Testing Google Docs file: ${googleDocFile.name}`);
            const docDetails = await googleDriveAPI.getFile(testUserEmail, googleDocFile.id);
            console.log('✅ Google Docs file test successful!');
            console.log(`   - Export links available: ${Object.keys(docDetails.exportLinks || {}).length}`);
        } else {
            console.log('ℹ️ No Google Docs files found for testing');
        }
        
        console.log('🎉 All tests passed! downloadUrl fix is working correctly.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        
        if (error.message.includes('Invalid field selection downloadUrl')) {
            console.error('💥 downloadUrl error still exists! Fix not working.');
        } else {
            console.error('🔍 Different error occurred:', error.message);
        }
        
        process.exit(1);
    }
}

// Run test
testDownloadUrlFix()
    .then(() => {
        console.log('✅ Test completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
