/**
 * Test script để kiểm tra download worker và thống kê lỗi
 */

import { DownloadWorker } from './src/services/download-worker.js';
import { SupabaseClient } from './src/database/supabase.js';
import dotenv from 'dotenv';

dotenv.config();

async function testDownloadWorker() {
    try {
        console.log('🧪 Testing download worker...');
        
        const supabase = new SupabaseClient();
        
        // Tạo một test session
        const testSession = {
            id: 'test-session-' + Date.now(),
            name: 'Test Session',
            download_path: '/tmp/test-download',
            concurrent_downloads: 1,
            max_retries: 2,
            stop_on_error: false,
            continue_on_error: true
        };
        
        console.log(`📋 Creating test session: ${testSession.id}`);
        
        // Insert test session vào database
        const { error: sessionError } = await supabase.getServiceClient()
            .from('download_sessions')
            .insert(testSession);
            
        if (sessionError) {
            throw new Error(`Failed to create test session: ${sessionError.message}`);
        }
        
        // Tạo một test download item với file ID không tồn tại để trigger lỗi
        const testItem = {
            id: 'test-item-' + Date.now(),
            download_session_id: testSession.id,
            scanned_file_id: 'fake-scanned-file-id',
            user_email: '<EMAIL>',
            file_id: 'FAKE_FILE_ID_THAT_DOES_NOT_EXIST',
            file_name: 'Test File That Does Not Exist.txt',
            file_path: '/Test File That Does Not Exist.txt',
            file_size: 1000,
            mime_type: 'text/plain',
            is_folder: false,
            status: 'pending',
            retry_count: 0
        };
        
        console.log(`📄 Creating test download item: ${testItem.file_name}`);
        
        // Insert test item vào database
        const { error: itemError } = await supabase.getServiceClient()
            .from('download_items')
            .insert(testItem);
            
        if (itemError) {
            throw new Error(`Failed to create test item: ${itemError.message}`);
        }
        
        // Tạo download worker
        const worker = new DownloadWorker(testSession.id, testSession, supabase);
        
        console.log('🚀 Starting download worker...');
        
        // Listen to events
        worker.on('progress', (data) => {
            console.log(`📊 Progress: Downloaded: ${data.downloadedFiles}, Failed: ${data.failedFiles}, Skipped: ${data.skippedFiles}`);
        });
        
        worker.on('completed', (stats) => {
            console.log('✅ Worker completed with stats:', stats);
        });
        
        worker.on('error', (error) => {
            console.log('❌ Worker error:', error.message);
        });
        
        // Start worker và chờ hoàn thành
        await new Promise((resolve, reject) => {
            worker.on('completed', resolve);
            worker.on('error', reject);
            
            worker.start();
            
            // Timeout sau 30 giây
            setTimeout(() => {
                reject(new Error('Test timeout'));
            }, 30000);
        });
        
        // Kiểm tra session stats trong database
        const { data: finalSession, error: fetchError } = await supabase.getServiceClient()
            .from('download_sessions')
            .select('*')
            .eq('id', testSession.id)
            .single();
            
        if (fetchError) {
            throw new Error(`Failed to fetch final session: ${fetchError.message}`);
        }
        
        console.log('📊 Final session stats:');
        console.log(`   - Downloaded files: ${finalSession.downloaded_files || 0}`);
        console.log(`   - Failed files: ${finalSession.failed_files || 0}`);
        console.log(`   - Status: ${finalSession.status}`);
        
        // Kiểm tra download items
        const { data: items, error: itemsError } = await supabase.getServiceClient()
            .from('download_items')
            .select('*')
            .eq('download_session_id', testSession.id);
            
        if (itemsError) {
            throw new Error(`Failed to fetch items: ${itemsError.message}`);
        }
        
        console.log('📄 Download items:');
        items.forEach(item => {
            console.log(`   - ${item.file_name}: ${item.status} (retries: ${item.retry_count})`);
            if (item.error_message) {
                console.log(`     Error: ${item.error_message}`);
            }
        });
        
        // Cleanup
        console.log('🧹 Cleaning up test data...');
        await supabase.getServiceClient()
            .from('download_items')
            .delete()
            .eq('download_session_id', testSession.id);
            
        await supabase.getServiceClient()
            .from('download_sessions')
            .delete()
            .eq('id', testSession.id);
        
        // Kiểm tra kết quả
        if (finalSession.failed_files > 0) {
            console.log('✅ Test passed! Failed files were correctly counted.');
        } else {
            console.log('❌ Test failed! Failed files were not counted correctly.');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run test
testDownloadWorker()
    .then(() => {
        console.log('✅ Test completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
